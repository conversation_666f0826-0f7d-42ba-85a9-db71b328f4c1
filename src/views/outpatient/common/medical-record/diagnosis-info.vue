<template>
    <div v-abc-click-outside="outside" class="aide-diagnosis-wrapper">
        <div class="diagnosis-social-info-wrapper">
            <abc-edit-div
                ref="ref-target"
                :key="forceUpdateFlag"
                v-model="currentValue"
                :class="{ 'is-focus': showSuggestions }"
                :disabled="disabled"
                :xss-options="{
                    a: ['class', 'data-tipsy'],
                    i: ['class', 'contenteditable']
                }"
                :maxlength="3000"
                data-cy="abc-mr-诊断"
                @click="handleClick"
                @focus="handleFocus"
                @blur="handleBlur"
                @input="handleInput"
                @tab="handleTab"
                @down="handleDown"
                @up="handleUp"
                @enter="enterEvent"
            >
            </abc-edit-div>
            <div v-if="diagnosisCodeList.length" class="diagnosis-social-info-popover">
                <div v-for="(item, index) in diagnosisCodeList" :key="item.name + index" class="social-code">
                    <span>{{ item.name }}</span> <span>{{ item.code }}</span>
                </div>
            </div>
        </div>

        <input
            ref="abcinput"
            type="text"
            style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
            tabindex="-1"
            :value="currentValue"
        />

        <div
            v-if="showSuggestions && suggestions.length"
            ref="popper-target"
            :class="{
                fixed: fixed,
                'is-only-name': onlyDiagnosisName,
            }"
            class="medical-record-suggestions-wrapper search-results suggestions-wrapper is-diagnosis"
            :style="suggestionsStyle"
            data-cy="abc-mr-popover-诊断"
        >
            <dl
                v-for="(s, index) in suggestions"
                :key="s.id"
                class="suggestions-item"
                :class="{ selected: index === currentIndex }"
                :data-cy="`abc-mr-诊断-${s.name}`"
                @mousedown.stop.prevent="selectAideDisease(s)"
            >
                <template v-if="onlyDiagnosisName">
                    <dt> {{ s.name }} </dt>
                </template>
                <template v-else>
                    <dt class="diagnosis-name">
                        <abc-space>
                            {{ s.name }}
                            <abc-text
                                v-if="s.clinicalDiseaseSimpleStr"
                                :title="s.clinicalDiseaseSimpleStr"
                                theme="gray"
                                class="clinical-disease-simple"
                            >
                                适用国临：{{ s.clinicalDiseaseSimpleStr }}
                            </abc-text>
                        </abc-space>
                    </dt>

                    <dt v-if="s.isSlowDisease" class="diagnosis-social-type">
                        <span class="ellipsis">
                            慢特病已备案
                        </span>
                    </dt>

                    <dt class="diagnosis-social-type">
                        <span class="ellipsis">
                            {{ s.hint || '' }}
                        </span>
                    </dt>

                    <dt class="diagnosis-social-code abc-tipsy abc-tipsy--w" :data-tipsy="`${s.hint }：${ s.code}`">
                        <span class="ellipsis">
                            {{ s.code || '' }}
                        </span>
                    </dt>

                    <template v-if="$abcSocialSecurity.config.isHeilongjiangHarbin && s.code">
                        <dt class="diagnosis-social-type" :title="s.hint">
                            <span class="ellipsis">
                                国临代码
                            </span>
                        </dt>
                        <dt class="diagnosis-social-code abc-tipsy abc-tipsy--w" :data-tipsy="s.code">
                            <span class="ellipsis">
                                {{ s.code || '' }}
                            </span>
                        </dt>
                    </template>
                </template>
            </dl>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import SocialAPI from 'api/social.js';

    // utils
    import { debounce } from 'utils/lodash';
    import { mapGetters } from 'vuex';
    import common from 'components/common/form';
    import popper from './popper';
    import {
        getWarnDiagnosisHtml,
        getWarnSyndromeHtml,
        isContainChineseDiagnosis,
    } from './utils.js';
    import diagnosisSocialCodeHandle from 'src/views/common/diagnosis-social-code-handle';
    import { isJSON } from '@/utils';
    import { keepLastIndex } from '@/utils/dom.js';
    import Clone from 'utils/clone';
    import { loosePlainText } from 'utils/xss-filter.js';
    import { DiagnosisSearchTypeEnum } from '@/common/constants/shebao.js';

    export default {
        name: 'Diagnosis',

        components: {
        },

        mixins: [
            common,
            popper,
            diagnosisSocialCodeHandle,
        ],
        props: {
            value: {
                type: Array,
                default: () => {
                    return [];
                },
                required: true,
            },
            diagnosis: {
                type: String,
                default: '',
            },
            count: {
                type: Number,
                default: 0,
            },
            postData: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            fetchSuggestions: Function, // 获取is-focus展开项
            placeholder: String,
            disabled: Boolean,
            medicalRecordType: Number,

            // 是否需要智能推荐
            needAideSuggestions: {
                type: Boolean,
                default: true,
            },
            // 是否需要匹配疾病编码
            mustMatchDiseaseCode: {
                type: Boolean,
                default: false,
            },
            // 至少包含一个西医诊断
            mustAtLeastOneWesternDisease: {
                type: Boolean,
                default: false,
            },
            onlyDiagnosisName: {
                type: Boolean,
                default: false,
            },
            diagnosisSearchType: [String, Number],
            extendDiagnosisInfos: Array,
        },
        data() {
            return {
                currentValue: '',
                currentList: [],
                showSuggestions: false,
                suggestions: [],
                asyncValue: '',
                loading: false,
                suggestionsStyle: {},

                currentIndex: -1,
                isFocusing: false,

                forceUpdateFlag: 0,
                appendedTips: false,
            };
        },

        computed: {
            ...mapGetters(['isSupportWeijianCenterMatchCode']),
            ...mapGetters('aiDiagnosis', [
                'diseases',
                'sickness',
            ]),
            isSyndrome() {
                return this.diagnosisSearchType === DiagnosisSearchTypeEnum.SYNDROME;
            },
            aides() {
                if (this.medicalRecordType === 1) {
                    const list = [];
                    this.sickness.map((item) => {
                        const {
                            diseaseGuid,
                            diseaseName,
                            diseaseStdCode,
                            stdCodeVersion,
                        } = item || {};

                        if (!list.find((it) => it.name === diseaseName)) {
                            list.push({
                                id: diseaseGuid,
                                name: diseaseName,
                                diseaseStdCode,
                                hint: stdCodeVersion || '',
                                code: diseaseStdCode || '',
                            });
                        }
                    });
                    if (list.length) {
                        // eslint-disable-next-line vue/no-async-in-computed-properties
                        this.$nextTick(() => {
                            this.initPopper();
                        });
                    }
                    return list;
                }
                if (this.diseases.length) {
                    // eslint-disable-next-line vue/no-async-in-computed-properties
                    this.$nextTick(() => {
                        this.initPopper();
                    });
                }
                return this.diseases.map((item) => {
                    const {
                        diseaseGuid,
                        diseaseName,
                        diseaseStdCode,
                        stdCodeVersion,
                    } = item || {};

                    return {
                        id: diseaseGuid,
                        name: diseaseName,
                        diseaseStdCode,
                        hint: stdCodeVersion || '',
                        code: diseaseStdCode || '',
                    };
                });
            },

            diagnosisCodeList() {
                if (!this.currentValue) return [];
                if (!this.$abcSocialSecurity.config.isHeilongjiangHarbin) return [];

                return this.currentList.filter((item) => {
                    return item.code;
                });
            },

            nationalDiseaseCodes () {
                const { shebaoCardInfo } = this.postData;
                return shebaoCardInfo?.extend?.chronicDiseaseMaintainInfo?.map((x) => x.value) ?? [];
            },
        },

        watch: {
            value: {
                handler (val) {
                    if (!val) {
                        this.asyncValue = '';
                        this.suggestions = [];
                        this.currentIndex = -1;
                    }
                    this.initCurrentList(val);
                },
                immediate: true,
                deep: true,
            },
            currentValue() {
                this.updateStyle();
            },
            disabled() {
                this.trans2Str();
                this.formatWarnText();
                this.initPlaceholderTips();
            },
            extendDiagnosisInfos: {
                handler() {
                    if (this.isSyndrome) {
                        this.formatWarnText();
                        this.initPlaceholderTips();
                    }
                },
                deep: true,
            },
        },
        created() {
            // 注册防抖search函数
            this._debounceSearch = debounce(this.queryDiagnosisAsync, 200, true);

            if (!this.isSyndrome) {
                this.$abcEventBus.$on('auto-search-diagnosis', this.onAutoSearch, this);
            }
        },
        mounted() {
            this.initPlaceholderTips();
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            initCurrentList(val) {
                // 新的接口用 extendDiagnosisInfos 保存诊断信息，判断有就用
                if (val && val.length) {
                    this.currentList = Clone(val);
                } else {
                    // 兼容 '["xxx","yyy"]' || '1.xxx，2.yyy' || 'xxx，yyy'
                    if (isJSON(this.diagnosis)) {
                        const _arr = JSON.parse(this.diagnosis);
                        this.currentList = _arr.map((it) => {
                            return {
                                name: it,
                                code: '',
                                diseaseType: null,
                            };
                        });
                    } else {
                        this.trans2Json(this.diagnosis);
                    }
                    // diagnosis有，diagnosisInfos没有的情况，需要更新一下diagnosisInfos
                    if (this.diagnosis) {
                        this.emitDataHandler();
                    }
                }
                this.trans2Str();
                this.formatWarnText();
            },

            trans2Json(val) {
                val = val.replace(/\u200b/g, '');

                let _arr = val.split(/<i contenteditable="false">，<\/i>/);

                _arr = _arr.map((it) => {
                    return it.replace(/【规】$/, '')
                        .replace(/^(<br>)*|(<br>)*$/g, '')
                        .replace(/^[,，、;。]*|[,，、;。]*$/g, '')
                        .trim();
                }).filter((it) => it);

                this.currentList = _arr.map((name) => {
                    const _obj = {
                        name,
                    };
                    const res = this.currentList.find((it) => {
                        return it.name === name;
                    });
                    if (res) {
                        Object.assign(_obj, res);
                    }
                    return _obj;
                });
            },

            trans2Str() {
                this.currentValue = this.currentList.map((item) => {
                    let { name } = item;
                    const { diseaseType } = item;
                    if (diseaseType === '4') {
                        name += '【规】';
                    }
                    return name;
                }).join('<i contenteditable="false">，</i>');
            },

            formatWarnText() {
                if (this.disabled) return;

                this.trans2Str();

                // 被focus不用请求code标黄
                if (this.isFocusing) return;
                // 不需要匹配code 不用请求code标黄
                if (!this.mustMatchDiseaseCode && !this.mustAtLeastOneWesternDisease) return;
                // 失焦后请求过程中 不用请求code标黄
                if (this._delayWarn) return;

                if (this.isSyndrome) {
                    this.currentValue = getWarnSyndromeHtml({
                        currentList: this.currentList,
                        diagnosisInfos: this.extendDiagnosisInfos,
                    });
                } else {
                    this.currentValue = getWarnDiagnosisHtml(this.currentList, this.mustMatchDiseaseCode, this.mustAtLeastOneWesternDisease, this.isSupportWeijianCenterMatchCode);
                }

                this.$nextTick(() => {
                    const { forceUpdate } = this.$refs['ref-target'] || {};
                    if (typeof forceUpdate === 'function') {
                        forceUpdate();
                    }
                });
            },
            handleClick() {
                if (this.disabled) return false;
                this.updateStyle();
                if (this.needAideSuggestions && !this.isSyndrome) {
                    this.suggestions = this.aides;
                    this.showSuggestions = true;
                    this.currentIndex = 0;
                }
                this.removePlaceholder();
            },
            handleTab() {
                this.showSuggestions = false;
            },
            handleFocus(e) {
                this.isFocusing = true;
                this.formatWarnText();
                if (this.currentValue) {
                    this.currentValue = this.currentValue.replace(/\u200b/g, '');
                    this.currentValue += '<i contenteditable="false">，</i>\u200b';
                    this.$nextTick(() => {
                        const { forceUpdate } = this.$refs['ref-target'] || {};
                        if (typeof forceUpdate === 'function') {
                            forceUpdate();
                        }
                    });
                    this._timer = setTimeout(() => {
                        keepLastIndex(e.target);
                    }, 1);
                }
            },
            async handleBlur() {
                this.isFocusing = false;
                this.currentValue = this.currentValue.replace(/<i contenteditable="false">，<\/i>\u200b?$/, '');
                this._delayWarn = true;
                this.emitDataHandler();
                // 需要code用于医保的地区，这里需要取换code
                if (this.mustMatchDiseaseCode && this.currentList && !this.isSyndrome) {
                    const extendDiagnosisInfos = await this.validateSocialCode({
                        extendDiagnosisInfos: [{
                            value: this.currentList,
                        }],
                    }, this.diagnosisSearchType);
                    if (extendDiagnosisInfos && extendDiagnosisInfos[0]) {
                        const queryCodeList = extendDiagnosisInfos[0].value || [];
                        this.currentList.forEach((item) => {
                            const result = queryCodeList.find((it) => item.name === it.name);
                            if (result) {
                                Object.assign(item, {
                                    code: result.code,
                                    diseaseType: result.diseaseType,
                                    hint: result.hint,
                                    nationalDisecode: result.nationalDisecode,
                                });
                            }
                        });
                        this.emitDataHandler();
                    }
                    this._delayWarn = false;
                    this.formatWarnText();
                }

                this.initPlaceholderTips();
                this._timer && clearTimeout(this._timer);
            },

            handleInput(val) {
                this.currentValue = val.replace(/(<i contenteditable="false">，<\/i>)*(\u200b)*(<br\s*\/*>)*$/, '');
                this.trans2Json(this.currentValue);
                this._debounceSearch();
            },

            // click outside 回调方法
            outside() {
                this.showSuggestions = false;
            },

            updateStyle() {
                this.suggestionsStyle = {
                    top: this.fixed ? '0' : `${this.$refs['ref-target'].$refs.abcinput.offsetHeight + 4}px`,
                };
            },

            handleDown(e) {
                if (this.showSuggestions && this.suggestions.length) {
                    if (e.isComposing) {
                        e.preventDefault();
                        e.stopPropagation();
                        return;
                    }
                    if (this.currentIndex < this.suggestions.length - 1) {
                        this.currentIndex++;
                    }
                    e.stopPropagation();
                    this.changeHighLight();
                    return;
                }
                this.showSuggestions = false;
            },
            handleUp(e) {
                if (this.showSuggestions && this.suggestions.length) {
                    if (e.isComposing) {
                        e.preventDefault();
                        e.stopPropagation();
                        return;
                    }
                    if (this.currentIndex > 0) {
                        this.currentIndex--;
                    }
                    e.stopPropagation();
                    this.changeHighLight();
                    return;
                }
                this.showSuggestions = false;
            },
            changeHighLight() {
                const index = this.currentIndex;
                if (index < 0) return false;
                const suggestions = this.$refs['popper-target'];
                const suggestionList = suggestions.querySelectorAll('.suggestions-item');

                const highlightItem = suggestionList[index];
                const { scrollTop } = suggestions;
                const { offsetTop } = highlightItem;

                const { scrollHeight } = highlightItem;

                if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                    suggestions.scrollTop += scrollHeight;
                }
                if (offsetTop < scrollTop) {
                    suggestions.scrollTop -= scrollHeight;
                }
            },
            enterEvent(e) {
                // 输入法过程中不响应回车选中
                if (e.isComposing) return;
                e.preventDefault && e.preventDefault();
                if (this.showSuggestions && this.suggestions.length) {
                    this.$emit('enterSelect', e);
                    if (this.currentIndex > -1) {
                        const _obj = this.suggestions[this.currentIndex];
                        this.selectAideDisease(_obj);
                    }
                    return false;
                }
            },

            async queryDiagnosisAsync() {
                const _arr = this.currentValue.split('<i contenteditable="false">，</i>');
                this.asyncValue = _arr[_arr.length - 1] || '';
                this.asyncValue = this.asyncValue.replace(/\u200b|<\/?br>/g, '');
                if (this.asyncValue.trim() && this.asyncValue.length < 100) {
                    const { data } = await SocialAPI.searchSocialDiagnosis({
                        keyword: this.asyncValue,
                        type: this.diagnosisSearchType,
                    });
                    const { diagnosisInfos } = data || {};
                    if (diagnosisInfos && diagnosisInfos.length && this.asyncValue.trim()) {
                        diagnosisInfos.length = diagnosisInfos.length > 10 ? 10 : diagnosisInfos.length;
                        this.suggestions = diagnosisInfos.map((item) => {
                            return {
                                name: item.name,
                                code: item.code,
                                diseaseType: item.diseaseType,
                                hint: item.hint,
                                isSlowDisease: this.nationalDiseaseCodes.includes(item.nationalDisecode),
                                clinicalDiseaseSimpleStr: item.clinicalDiseaseSimpleList && item.clinicalDiseaseSimpleList.map((v) => v.name).join('；'),
                            };
                        });
                    } else {
                        this.suggestions = [];
                    }
                } else {
                    this.suggestions = [];
                }
                if (this.suggestions.length) {
                    this.currentIndex = 0;
                    this.showSuggestions = false;
                    this.$nextTick(() => {
                        this.showSuggestions = true;
                    });
                } else {
                    this.currentIndex = -1;
                }
            },

            selectAideDisease(aide, replaceSearchVal = true) {
                const {
                    name,
                    code,
                    diseaseType,
                    nationalDisecode,
                    hint,
                } = aide;
                if (!name) return;

                const isExist = this.currentList.find((it) => {
                    return it.name !== this.asyncValue && it.name === name;
                });
                if (isExist) return;

                if (this.asyncValue && replaceSearchVal) {
                    try {
                        const reg = new RegExp(`${this.asyncValue}(</?br>)*$`);
                        this.currentValue = this.currentValue.replace(reg, '');
                        this.trans2Json(this.currentValue);
                    } catch (e) {
                        console.error('存在特殊符号');
                    }
                    this.asyncValue = '';
                }

                if (this.showSuggestions && this.suggestions.length) {
                    this.suggestions = [];
                    this.forceUpdateFlag = 1;
                    this.$nextTick(() => {
                        this.forceUpdateFlag = 0;
                    });
                }

                this.currentList.push({
                    code,
                    name,
                    diseaseType,
                    nationalDisecode,
                    hint,
                });
                this.trans2Str();
                this.emitDataHandler();

                this.asyncValue = '';
                this.suggestions = [];
                this.showSuggestions = false;
                this.$emit('changeDisease', aide);
            },

            emitDataHandler() {
                this.currentList = this.currentList.map((it) => {
                    it.name = loosePlainText(it.name);
                    return it;
                });
                const _value = this.currentList.map((it) => it.name.replace(/\u200b/g, '')).join('，');
                this.$emit('input', this.currentList);
                if (this.diagnosis) {
                    this.$emit('update:diagnosis', _value);
                }
                this.formItem && this.formItem.$emit('formFieldInput', _value);
            },

            async onAutoSearch() {
                if (!this.currentList.length) return;
                const { code } = this.currentList[this.currentList.length - 1];
                if (code) return;
                await this.$nextTick();
                this.$refs['ref-target'].$refs.abcinput.focus();
                this.handleInput(this.currentValue);
            },

            // 需要一个tipsy的placeholder，edit-div不支持，模拟一个
            initPlaceholderTips() {
                if (!this.isSyndrome) return;
                this.removePlaceholder();
                if (this.disabled) return;
                if (this.currentList.length) return;
                const hasChineseDiagnosis = isContainChineseDiagnosis(this.extendDiagnosisInfos);
                if (!hasChineseDiagnosis) return;
                this.createPlaceholder();
            },
            createPlaceholder() {
                const link = document.createElement('a');
                link.textContent = '医保要求开立中医标准证候';
                link.style.color = '#AEA97F';
                link.setAttribute('data-tipsy', '医保结算要求必须开立至少一条中医标准证候，若不使用医保结算请忽略此提示');
                link.setAttribute('class', 'abc-tipsy--n shortage-tips fake-placeholder');

                // 添加点击事件监听器，点击时聚焦到输入框
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // 聚焦到 abc-edit-div 组件
                    this.removePlaceholder();
                    this.$refs['ref-target'].$el.focus();
                });
                this.$refs['ref-target'].$el.appendChild(link);
                this.appendedTips = true;
            },
            removePlaceholder() {
                if (!this.isSyndrome) return;
                const placeholderEle = this.$refs['ref-target'].$el.querySelector('.fake-placeholder');
                if (placeholderEle) {
                    placeholderEle.remove();
                }
                this.appendedTips = false;
            },
        },
    };
</script>
