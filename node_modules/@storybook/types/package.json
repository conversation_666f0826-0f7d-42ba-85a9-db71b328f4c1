{"name": "@storybook/types", "version": "7.2.0", "description": "Core Storybook TS Types", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/types", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/types"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts"], "scripts": {"check": "../../../scripts/prepare/check.ts", "prep": "../../../scripts/prepare/bundle.ts"}, "dependencies": {"@storybook/channels": "7.2.0", "@types/babel__core": "^7.0.0", "@types/express": "^4.7.0", "file-system-cache": "2.3.0"}, "devDependencies": {"@storybook/csf": "^0.1.0", "@types/node": "^16.0.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}