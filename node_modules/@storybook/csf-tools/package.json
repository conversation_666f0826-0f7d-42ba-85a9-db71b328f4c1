{"name": "@storybook/csf-tools", "version": "7.6.20", "description": "Parse and manipulate CSF and Storybook config files", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/csf-tools", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/csf-tools"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@babel/generator": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.2", "@babel/types": "^7.23.0", "@storybook/csf": "^0.1.2", "@storybook/types": "7.6.20", "fs-extra": "^11.1.0", "recast": "^0.23.1", "ts-dedent": "^2.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.1", "@types/js-yaml": "^4.0.5", "js-yaml": "^4.1.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"], "formats": ["cjs"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}