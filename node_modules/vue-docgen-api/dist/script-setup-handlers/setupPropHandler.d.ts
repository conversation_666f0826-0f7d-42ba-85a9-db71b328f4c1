import * as bt from '@babel/types';
import { NodePath } from 'ast-types/lib/node-path';
import type { Documentation } from '../Documentation';
/**
 * Extract information from an setup-style VueJs 3 component
 * about what props can be used with this component
 * @param {NodePath} astPath
 * @param {Array<NodePath>} componentDefinitions
 * @param {string} originalFilePath
 */
declare const _default: (documentation: Documentation, componentDefinition: NodePath<any, any>, astPath: bt.File, opt: import("../types").ParseOptions) => Promise<void>;
export default _default;
export declare function getPropsFromLiteralType(documentation: Documentation, typeParamsPathMembers: any): void;
