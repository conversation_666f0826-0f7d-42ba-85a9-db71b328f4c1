"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var setupEventHandler_1 = __importDefault(require("./setupEventHandler"));
var setupOptionsHandler_1 = __importDefault(require("./setupOptionsHandler"));
var setupPropHandler_1 = __importDefault(require("./setupPropHandler"));
var setupExposeHandler_1 = __importDefault(require("./setupExposeHandler"));
var setupSlotHandler_1 = __importDefault(require("./setupSlotHandler"));
exports.default = [setupEventHandler_1.default, setupOptionsHandler_1.default, setupPropHandler_1.default, setupExposeHandler_1.default, setupSlotHandler_1.default];
